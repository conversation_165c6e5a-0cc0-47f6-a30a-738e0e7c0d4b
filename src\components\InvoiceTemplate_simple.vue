<template>
  <div class="invoice-template">
    <div class="print-actions">
      <a-button type="primary" size="large" @click="handlePrint" class="print-button">
        <PrinterOutlined />
        Print / Save as PDF
      </a-button>
    </div>

    <div class="invoice-content" id="invoice-content">
      <h1>Invoice Test</h1>
      <p>Invoice Type: {{ invoiceData.type }}</p>
      <p>Invoice Number: {{ invoiceData.invoiceNumber }}</p>
      <p>Amount: {{ formatCurrency(invoiceData.amount) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PrinterOutlined } from '@ant-design/icons-vue'
import type { InvoiceData } from '@/types'
import { invoiceTemplates, formatCurrency } from '@/utils/invoiceGenerator'

interface Props {
  invoiceData: InvoiceData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  print: []
}>()

const template = computed(() => {
  return invoiceTemplates[props.invoiceData.type]
})

const handlePrint = () => {
  emit('print')
}
</script>

<style scoped>
.invoice-template {
  max-width: 800px;
  margin: 0 auto;
}

.print-actions {
  margin-bottom: 24px;
  text-align: right;
}

.print-button {
  background: #1890ff;
  border-color: #1890ff;
  height: 40px;
  padding: 0 24px;
  font-weight: 500;
}

.invoice-content {
  background: white;
  padding: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}
</style>
