<template>
    <div class="invoice-generator">
        <!-- 页面头部 -->
        <header class="header">
            <div class="container">
                <h1 class="title">WiPDF Invoice Generator</h1>
                <p class="subtitle">Generate professional invoices instantly</p>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="container">
                <div class="content-grid">
                    <!-- 左侧控制面板 -->
                    <div class="control-panel">
                        <div class="panel-content">
                            <h2>Generate Invoice</h2>

                            <!-- Invoice类型选择 -->
                            <div class="form-group">
                                <label class="form-label">Select Invoice Type</label>
                                <a-radio-group v-model:value="formData.invoiceType" class="invoice-type-group">
                                    <a-radio value="windsurf" class="invoice-type-option">
                                        <div class="option-content">
                                            <span class="option-name">Windsurf Invoice</span>
                                            <span class="option-price">$6.90</span>
                                        </div>
                                    </a-radio>
                                    <a-radio value="cursor" class="invoice-type-option">
                                        <div class="option-content">
                                            <span class="option-name">Cursor Invoice</span>
                                            <span class="option-price">$20.00</span>
                                        </div>
                                    </a-radio>
                                </a-radio-group>
                            </div>

                            <!-- 邮箱输入 -->
                            <div class="form-group">
                                <label class="form-label required">Email Address</label>
                                <a-input v-model:value="formData.email" placeholder="Enter your email address"
                                    size="large" :status="emailError ? 'error' : ''" @blur="validateEmailInput" />
                                <div v-if="emailError" class="error-message">
                                    {{ emailError }}
                                </div>
                            </div>

                            <!-- 生成按钮 -->
                            <div class="form-group">
                                <a-button type="primary" size="large" block :loading="isGenerating"
                                    :disabled="!isFormValid" @click="generateInvoice" class="generate-button">
                                    Generate New Invoice
                                </a-button>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧Invoice预览 -->
                    <div class="preview-panel">
                        <div class="preview-content">
                            <div v-if="!currentInvoice" class="empty-state">
                                <div class="empty-icon">
                                    <FileTextOutlined />
                                </div>
                                <h3>No Invoice Generated</h3>
                                <p>Fill in the form and click "Generate New Invoice" to create your invoice.</p>
                            </div>

                            <!-- Invoice组件将在这里显示 -->
                            <InvoiceTemplate v-if="currentInvoice" :invoice-data="currentInvoice"
                                @print="handlePrint" />
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 页脚说明 -->
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="feature-list">
                        <div class="feature-item">
                            <CheckCircleOutlined />
                            <span>Instant Generation</span>
                        </div>
                        <div class="feature-item">
                            <CheckCircleOutlined />
                            <span>Professional Templates</span>
                        </div>
                        <div class="feature-item">
                            <CheckCircleOutlined />
                            <span>PDF Export</span>
                        </div>
                        <div class="feature-item">
                            <CheckCircleOutlined />
                            <span>No Registration Required</span>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { FileTextOutlined, CheckCircleOutlined } from '@ant-design/icons-vue'
import type { FormData, InvoiceData, InvoiceType } from '@/types'
import { generateInvoiceData, validateEmail, printInvoice } from '@/utils/invoiceGenerator'
import InvoiceTemplate from '@/components/InvoiceTemplate.vue'

// 响应式数据
const formData = ref<FormData>({
    email: '',
    invoiceType: 'windsurf'
})

const currentInvoice = ref<InvoiceData | null>(null)
const isGenerating = ref(false)
const emailError = ref('')

// 计算属性
const isFormValid = computed(() => {
    return formData.value.email && validateEmail(formData.value.email) && !emailError.value
})

// 验证邮箱输入
const validateEmailInput = () => {
    if (!formData.value.email) {
        emailError.value = 'Email is required'
    } else if (!validateEmail(formData.value.email)) {
        emailError.value = 'Please enter a valid email address'
    } else {
        emailError.value = ''
    }
}

// 生成Invoice
const generateInvoice = async () => {
    if (!isFormValid.value) {
        message.error('Please fill in all required fields correctly')
        return
    }

    isGenerating.value = true

    try {
        // 模拟生成延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 生成Invoice数据
        currentInvoice.value = generateInvoiceData(formData.value.email, formData.value.invoiceType)

        message.success('Invoice generated successfully!')
    } catch (error) {
        message.error('Failed to generate invoice. Please try again.')
    } finally {
        isGenerating.value = false
    }
}

// 处理打印
const handlePrint = async () => {
    await printInvoice()
}
</script>

<style scoped>
.invoice-generator {
    min-height: 100vh;
    background: #f5f7fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.title {
    font-size: 48px;
    font-weight: 700;
    margin: 0 0 16px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 20px;
    margin: 0;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    padding: 60px 0;
}

.content-grid {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 40px;
    align-items: start;
}

/* 控制面板 */
.control-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-content {
    padding: 32px;
}

.panel-content h2 {
    font-size: 24px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 32px 0;
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 8px;
}

.form-label.required::after {
    content: ' *';
    color: #ff4d4f;
}

.invoice-type-group {
    width: 100%;
}

.invoice-type-option {
    display: block !important;
    width: 100%;
    padding: 16px;
    border: 2px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.invoice-type-option:hover {
    border-color: #1890ff;
}

.invoice-type-option.ant-radio-wrapper-checked {
    border-color: #1890ff;
    background: #e6f7ff;
}

.option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 8px;
}

.option-name {
    font-weight: 500;
    color: #262626;
}

.option-price {
    font-weight: 600;
    color: #1890ff;
    font-size: 16px;
}

.error-message {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
}

.generate-button {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    background: #1890ff;
    border-color: #1890ff;
}

.generate-button:hover {
    background: #40a9ff;
    border-color: #40a9ff;
}

/* 预览面板 */
.preview-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-height: 600px;
}

.preview-content {
    padding: 32px;
}

.empty-state {
    text-align: center;
    padding: 80px 40px;
    color: #8c8c8c;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 24px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 20px;
    font-weight: 500;
    margin: 0 0 12px 0;
    color: #595959;
}

.empty-state p {
    font-size: 14px;
    line-height: 1.6;
    margin: 0;
}

/* 页脚 */
.footer {
    background: #fafafa;
    padding: 40px 0;
    border-top: 1px solid #f0f0f0;
}

.footer-content {
    text-align: center;
}

.feature-list {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1890ff;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .control-panel {
        order: 1;
    }

    .preview-panel {
        order: 2;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }

    .header {
        padding: 40px 0;
    }

    .title {
        font-size: 32px;
    }

    .subtitle {
        font-size: 16px;
    }

    .main-content {
        padding: 40px 0;
    }

    .panel-content {
        padding: 24px;
    }

    .preview-content {
        padding: 24px;
    }

    .feature-list {
        gap: 24px;
    }

    .feature-item {
        font-size: 14px;
    }
}
</style>