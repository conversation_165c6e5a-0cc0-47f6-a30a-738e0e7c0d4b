<template>
  <div class="invoice-template" :class="'invoice-' + invoiceData.type">
    <!-- 打印按钮 -->
    <div class="print-actions">
      <a-button type="primary" size="large" @click="handlePrint" class="print-button">
        <PrinterOutlined />
        Print / Save as PDF
      </a-button>
    </div>

    <!-- Invoice内容 -->
    <div class="invoice-content" id="invoice-content">
      <!-- Cursor Receipt 样式 -->
      <div v-if="invoiceData.type === 'cursor'" class="cursor-receipt">
        <!-- 头部 -->
        <div class="receipt-header">
          <h1 class="receipt-title">Receipt</h1>
          <div class="cursor-logo">
            <img :src="cursorLogoUrl" alt="Cursor Logo" class="logo-img" />
          </div>
        </div>

        <!-- 基本信息表格 -->
        <table class="info-table">
          <tbody>
            <tr>
              <td class="info-label">Invoice number</td>
              <td class="info-value">{{ invoiceData.invoiceNumber }}</td>
            </tr>
            <tr>
              <td class="info-label">Receipt number</td>
              <td class="info-value">{{ invoiceData.receiptNumber }}</td>
            </tr>
            <tr>
              <td class="info-label">Date paid</td>
              <td class="info-value">{{ invoiceData.datePaid }}</td>
            </tr>
            <tr>
              <td class="info-label">Payment method</td>
              <td class="info-value">{{ invoiceData.paymentMethod }}</td>
            </tr>
          </tbody>
        </table>

        <!-- 公司和客户信息 -->
        <div class="contact-info">
          <div class="company-section">
            <p class="company-name">{{ template.companyInfo.name }}</p>
            <p v-for="line in template.companyInfo.address" :key="line">{{ line }}</p>
            <p v-if="template.companyInfo.phone">{{ template.companyInfo.phone }}</p>
            <p>{{ template.companyInfo.email }}</p>
          </div>
          <div class="customer-section">
            <p class="section-title">Bill to</p>
            <p class="customer-name">{{ invoiceData.billTo.name.toUpperCase() }}</p>
            <p v-for="line in invoiceData.billTo.address" :key="line">{{ line }}</p>
            <p>{{ invoiceData.billTo.email }}</p>
          </div>
        </div>

        <!-- 金额标题 -->
        <h2 class="amount-title">{{ formatCurrency(invoiceData.total) }} paid on {{ invoiceData.datePaid }}</h2>

        <!-- 项目详情表格 -->
        <table class="items-table">
          <thead>
            <tr>
              <th>Description</th>
              <th>Qty</th>
              <th>Unit price</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <div class="item-description">{{ invoiceData.description }}</div>
                <div class="item-period">{{ invoiceData.period }}</div>
              </td>
              <td>1</td>
              <td>{{ formatCurrency(invoiceData.amount) }}</td>
              <td>{{ formatCurrency(invoiceData.amount) }}</td>
            </tr>
          </tbody>
        </table>

        <!-- 总计表格 -->
        <table class="totals-table">
          <tbody>
            <tr>
              <td>Subtotal</td>
              <td>{{ formatCurrency(invoiceData.subtotal) }}</td>
            </tr>
            <tr>
              <td>Total</td>
              <td>{{ formatCurrency(invoiceData.total) }}</td>
            </tr>
            <tr class="amount-paid-row">
              <td><strong>Amount paid</strong></td>
              <td><strong>{{ formatCurrency(invoiceData.amountPaid) }}</strong></td>
            </tr>
          </tbody>
        </table>

        <!-- 页脚 -->
        <div class="receipt-footer">
          <p>Tax ID: {{ template.companyInfo.taxInfo }}</p>
        </div>

        <!-- 底部信息 -->
        <div class="bottom-info">
          <span>{{ bottomInfoText }}</span>
          <span>Page 1 of 1</span>
        </div>
      </div>

      <!-- Windsurf Invoice 样式 -->
      <div v-else class="windsurf-invoice">
        <!-- 头部 -->
        <div class="invoice-header">
          <div class="company-info">
            <div class="windsurf-logo">
              <img :src="windsurfLogoUrl" alt="Windsurf Logo" class="logo-img" />
            </div>
            <div class="company-details">
              <h1 class="company-name">{{ template.companyInfo.name }}</h1>
              <div class="company-address">
                <div v-for="line in template.companyInfo.address" :key="line">{{ line }}</div>
              </div>
              <div class="company-contact">
                <div>{{ template.companyInfo.email }}</div>
                <div v-if="template.companyInfo.phone">{{ template.companyInfo.phone }}</div>
              </div>
            </div>
          </div>

          <div class="invoice-meta">
            <h2 class="invoice-title">INVOICE</h2>
            <div class="invoice-details">
              <div class="detail-row">
                <span class="label">Invoice #:</span>
                <span class="value">{{ invoiceData.invoiceNumber }}</span>
              </div>
              <div class="detail-row">
                <span class="label">Receipt #:</span>
                <span class="value">{{ invoiceData.receiptNumber }}</span>
              </div>
              <div class="detail-row">
                <span class="label">Date Paid:</span>
                <span class="value">{{ invoiceData.datePaid }}</span>
              </div>
              <div class="detail-row">
                <span class="label">Payment Method:</span>
                <span class="value">{{ invoiceData.paymentMethod }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 收票人信息 -->
        <div class="bill-to-section">
          <h3 class="section-title">Bill To:</h3>
          <div class="bill-to-info">
            <div class="customer-name">{{ invoiceData.billTo.name }}</div>
            <div class="customer-email">{{ invoiceData.billTo.email }}</div>
            <div class="customer-address">
              <div v-for="line in invoiceData.billTo.address" :key="line">{{ line }}</div>
            </div>
          </div>
        </div>

        <!-- 项目明细 -->
        <div class="items-section">
          <table class="items-table">
            <thead>
              <tr>
                <th>Description</th>
                <th>Period</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{{ invoiceData.description }}</td>
                <td>{{ invoiceData.period }}</td>
                <td>{{ formatCurrency(invoiceData.amount) }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 总计 -->
        <div class="totals-section">
          <div class="totals-table">
            <div class="total-row">
              <span class="total-label">Subtotal:</span>
              <span class="total-value">{{ formatCurrency(invoiceData.subtotal) }}</span>
            </div>
            <div class="total-row total-final">
              <span class="total-label">Total:</span>
              <span class="total-value">{{ formatCurrency(invoiceData.total) }}</span>
            </div>
            <div class="total-row paid">
              <span class="total-label">Amount Paid:</span>
              <span class="total-value">{{ formatCurrency(invoiceData.amountPaid) }}</span>
            </div>
          </div>
        </div>

        <!-- 页脚 -->
        <div class="invoice-footer">
          <div class="footer-note">
            <p>Thank you for your business!</p>
            <p v-if="template.companyInfo.taxInfo">{{ template.companyInfo.taxInfo }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PrinterOutlined } from '@ant-design/icons-vue'
import type { InvoiceData } from '@/types'
import { invoiceTemplates, formatCurrency } from '@/utils/invoiceGenerator'

// Logo URLs (从public目录访问)
const windsurfLogoUrl = '/windsurf-logo.png'
const cursorLogoUrl = '/cursor-logo.png'

// Props
interface Props {
  invoiceData: InvoiceData
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  print: []
}>()

// 计算属性
const template = computed(() => {
  return invoiceTemplates[props.invoiceData.type]
})

const bottomInfoText = computed(() => {
  return `${props.invoiceData.receiptNumber} · ${formatCurrency(props.invoiceData.amountPaid)} paid on ${props.invoiceData.datePaid}`
})



// 处理打印
const handlePrint = () => {
  emit('print')
}
</script>

<style scoped>
.invoice-template {
  max-width: 800px;
  margin: 0 auto;
}

.print-actions {
  margin-bottom: 24px;
  text-align: right;
}

.print-button {
  background: #1890ff;
  border-color: #1890ff;
  height: 40px;
  padding: 0 24px;
  font-weight: 500;
}

.print-button:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.invoice-content {
  background: white;
  padding: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  color: #000;
}

/* Cursor Receipt 样式 */
.cursor-receipt {
  max-width: 600px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.receipt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.receipt-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #000;
}

.cursor-logo {
  width: 48px;
  height: 48px;
}

.logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 32px;
}

.info-table td {
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
}

.info-label {
  color: #666;
  width: 140px;
}

.info-value {
  color: #000;
  font-weight: 500;
}

.contact-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
  gap: 40px;
}

.company-section,
.customer-section {
  flex: 1;
}

.company-section p,
.customer-section p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #000;
  line-height: 1.4;
}

.company-name {
  font-weight: 600;
  margin-bottom: 8px !important;
}

.section-title {
  font-weight: 600;
  margin-bottom: 8px !important;
  color: #666;
}

.customer-name {
  font-weight: 600;
  margin-bottom: 4px !important;
}

.amount-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #000;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;
}

.items-table th {
  background: #f8f9fa;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  color: #000;
  border-bottom: 1px solid #e8e8e8;
}

.items-table td {
  padding: 12px;
  font-size: 14px;
  color: #000;
  border-bottom: 1px solid #e8e8e8;
}

.items-table th:last-child,
.items-table td:last-child {
  text-align: right;
}

.item-description {
  font-weight: 500;
  margin-bottom: 4px;
}

.item-period {
  color: #666;
  font-size: 13px;
}

.totals-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;
}

.totals-table td {
  padding: 8px 12px;
  font-size: 14px;
  border-bottom: 1px solid #e8e8e8;
}

.totals-table td:first-child {
  color: #666;
}

.totals-table td:last-child {
  text-align: right;
  color: #000;
  font-weight: 500;
}

.amount-paid-row td {
  font-weight: 600;
  color: #000;
}

.receipt-footer {
  margin-bottom: 40px;
  text-align: center;
}

.receipt-footer p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.bottom-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  font-size: 13px;
  color: #666;
}

/* Windsurf Invoice 样式 */
.windsurf-invoice {
  max-width: 700px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.windsurf-logo {
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
}

.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e8e8e8;
}

.windsurf-invoice .company-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.windsurf-invoice .company-details {
  flex: 1;
}

.windsurf-invoice .company-name {
  font-size: 20px;
  font-weight: 600;
  color: #000;
  margin: 0 0 8px 0;
}

.windsurf-invoice .company-address,
.windsurf-invoice .company-contact {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.windsurf-invoice .company-address div,
.windsurf-invoice .company-contact div {
  margin-bottom: 2px;
}

.windsurf-invoice .invoice-meta {
  text-align: right;
}

.windsurf-invoice .invoice-title {
  font-size: 28px;
  font-weight: 700;
  color: #000;
  margin: 0 0 16px 0;
}

.windsurf-invoice .invoice-details {
  min-width: 200px;
}

.windsurf-invoice .detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.windsurf-invoice .label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.windsurf-invoice .value {
  font-weight: 500;
  color: #000;
  font-size: 14px;
}

.windsurf-invoice .bill-to-section {
  margin-bottom: 32px;
}

.windsurf-invoice .section-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin: 0 0 12px 0;
}

.windsurf-invoice .bill-to-info .customer-name {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin-bottom: 4px;
}

.windsurf-invoice .customer-email,
.windsurf-invoice .customer-address {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.windsurf-invoice .customer-address div {
  margin-bottom: 2px;
}

.windsurf-invoice .items-section {
  margin-bottom: 32px;
}

.windsurf-invoice .items-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 32px;
}

.windsurf-invoice .items-table th,
.windsurf-invoice .items-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
}

.windsurf-invoice .items-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #000;
}

.windsurf-invoice .items-table td {
  color: #000;
}

.windsurf-invoice .items-table td:last-child,
.windsurf-invoice .items-table th:last-child {
  text-align: right;
}

.windsurf-invoice .totals-section {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 32px;
}

.windsurf-invoice .totals-table {
  min-width: 250px;
}

.windsurf-invoice .total-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
}

.windsurf-invoice .total-final {
  border-bottom: 2px solid #000;
  font-weight: 600;
}

.windsurf-invoice .paid {
  color: #1890ff;
  font-weight: 600;
  border-bottom: none;
}

.windsurf-invoice .total-label {
  color: #666;
}

.windsurf-invoice .total-value {
  color: #000;
  font-weight: 500;
}

.windsurf-invoice .invoice-footer {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #e8e8e8;
}

.windsurf-invoice .footer-note {
  color: #666;
  font-size: 14px;
}

.windsurf-invoice .footer-note p {
  margin: 4px 0;
}

/* 打印样式 */
@media print {
  .print-actions {
    display: none;
  }

  .invoice-content {
    border: none;
    box-shadow: none;
    padding: 20px;
  }

  .invoice-template {
    max-width: none;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .invoice-header {
    flex-direction: column;
    gap: 24px;
  }

  .windsurf-invoice .invoice-meta {
    text-align: left;
  }

  .invoice-content {
    padding: 24px;
  }

  .windsurf-invoice .company-info {
    flex-direction: column;
    gap: 12px;
  }

  .windsurf-invoice .detail-row {
    min-width: auto;
  }

  .contact-info {
    flex-direction: column;
    gap: 20px;
  }

  .cursor-receipt,
  .windsurf-invoice {
    max-width: 100%;
  }
}
</style>

<style scoped>
.invoice-template {
  max-width: 800px;
  margin: 0 auto;
}

.print-actions {
  margin-bottom: 24px;
  text-align: right;
}

.print-button {
  background: #1890ff;
  border-color: #1890ff;
  height: 40px;
  padding: 0 24px;
  font-weight: 500;
}

.print-button:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.invoice-content {
  background: white;
  padding: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  color: #000;
}

/* Cursor Receipt 样式 */
.cursor-receipt {
  max-width: 600px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.receipt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.receipt-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #000;
}

.cursor-logo {
  width: 48px;
  height: 48px;
}

.logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 32px;
}

.info-table td {
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
}

.info-label {
  color: #666;
  width: 140px;
}

.info-value {
  color: #000;
  font-weight: 500;
}

.contact-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
  gap: 40px;
}

.company-section,
.customer-section {
  flex: 1;
}

.company-section p,
.customer-section p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #000;
  line-height: 1.4;
}

.company-name {
  font-weight: 600;
  margin-bottom: 8px !important;
}

.section-title {
  font-weight: 600;
  margin-bottom: 8px !important;
  color: #666;
}

.customer-name {
  font-weight: 600;
  margin-bottom: 4px !important;
}

.amount-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #000;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;
}

.items-table th {
  background: #f8f9fa;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  color: #000;
  border-bottom: 1px solid #e8e8e8;
}

.items-table td {
  padding: 12px;
  font-size: 14px;
  color: #000;
  border-bottom: 1px solid #e8e8e8;
}

.items-table th:last-child,
.items-table td:last-child {
  text-align: right;
}

.item-description {
  font-weight: 500;
  margin-bottom: 4px;
}

.item-period {
  color: #666;
  font-size: 13px;
}

.totals-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;
}

.totals-table td {
  padding: 8px 12px;
  font-size: 14px;
  border-bottom: 1px solid #e8e8e8;
}

.totals-table td:first-child {
  color: #666;
}

.totals-table td:last-child {
  text-align: right;
  color: #000;
  font-weight: 500;
}

.amount-paid-row td {
  font-weight: 600;
  color: #000;
}

.receipt-footer {
  margin-bottom: 40px;
  text-align: center;
}

.receipt-footer p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.bottom-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  font-size: 13px;
  color: #666;
}

/* Windsurf Invoice 样式 */
.windsurf-invoice {
  max-width: 700px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.windsurf-logo {
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
}

.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e8e8e8;
}

.windsurf-invoice .company-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.windsurf-invoice .company-details {
  flex: 1;
}

.windsurf-invoice .company-name {
  font-size: 20px;
  font-weight: 600;
  color: #000;
  margin: 0 0 8px 0;
}

.windsurf-invoice .company-address,
.windsurf-invoice .company-contact {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.windsurf-invoice .company-address div,
.windsurf-invoice .company-contact div {
  margin-bottom: 2px;
}

.windsurf-invoice .invoice-meta {
  text-align: right;
}

.windsurf-invoice .invoice-title {
  font-size: 28px;
  font-weight: 700;
  color: #000;
  margin: 0 0 16px 0;
}

.windsurf-invoice .invoice-details {
  min-width: 200px;
}

.windsurf-invoice .detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.windsurf-invoice .label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.windsurf-invoice .value {
  font-weight: 500;
  color: #000;
  font-size: 14px;
}

/* Windsurf Invoice 收票人信息 */
.windsurf-invoice .bill-to-section {
  margin-bottom: 32px;
}

.windsurf-invoice .section-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin: 0 0 12px 0;
}

.windsurf-invoice .bill-to-info .customer-name {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin-bottom: 4px;
}

.windsurf-invoice .customer-email,
.windsurf-invoice .customer-address {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.windsurf-invoice .customer-address div {
  margin-bottom: 2px;
}

/* Windsurf Invoice 项目表格 */
.windsurf-invoice .items-section {
  margin-bottom: 32px;
}

.windsurf-invoice .items-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 32px;
}

.windsurf-invoice .items-table th,
.windsurf-invoice .items-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
}

.windsurf-invoice .items-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #000;
}

.windsurf-invoice .items-table td {
  color: #000;
}

.windsurf-invoice .items-table td:last-child,
.windsurf-invoice .items-table th:last-child {
  text-align: right;
}

/* Windsurf Invoice 总计部分 */
.windsurf-invoice .totals-section {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 32px;
}

.windsurf-invoice .totals-table {
  min-width: 250px;
}

.windsurf-invoice .total-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
}

.windsurf-invoice .total-final {
  border-bottom: 2px solid #000;
  font-weight: 600;
}

.windsurf-invoice .paid {
  color: #1890ff;
  font-weight: 600;
  border-bottom: none;
}

.windsurf-invoice .total-label {
  color: #666;
}

.windsurf-invoice .total-value {
  color: #000;
  font-weight: 500;
}

/* Windsurf Invoice 页脚 */
.windsurf-invoice .invoice-footer {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #e8e8e8;
}

.windsurf-invoice .footer-note {
  color: #666;
  font-size: 14px;
}

.windsurf-invoice .footer-note p {
  margin: 4px 0;
}

/* 打印样式 */
@media print {
  .print-actions {
    display: none;
  }

  .invoice-content {
    border: none;
    box-shadow: none;
    padding: 20px;
  }

  .invoice-template {
    max-width: none;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .invoice-header {
    flex-direction: column;
    gap: 24px;
  }

  .windsurf-invoice .invoice-meta {
    text-align: left;
  }

  .invoice-content {
    padding: 24px;
  }

  .windsurf-invoice .company-info {
    flex-direction: column;
    gap: 12px;
  }

  .windsurf-invoice .detail-row {
    min-width: auto;
  }

  .contact-info {
    flex-direction: column;
    gap: 20px;
  }

  .cursor-receipt,
  .windsurf-invoice {
    max-width: 100%;
  }
}
</style>
