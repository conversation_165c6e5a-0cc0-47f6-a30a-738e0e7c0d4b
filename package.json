{"name": "invoice-management", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=16.0.0"}, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "html2canvas": "^1.4.1", "vue": "^3.5.18"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^18.0.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/tsconfig": "^0.4.0", "typescript": "~5.0.0", "vite": "^4.0.0", "vue-tsc": "^1.0.0"}}